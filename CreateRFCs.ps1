﻿# -*- coding: utf-8-with-signature -*-
## Description
#
#  Create a temporary folder and copy the target script (Parameter
#  $ScriptName) to that location as well as all *.xlsx files in the
#  current directory. Execute the target script and then remove all
#  the items copied to the temp folder. Afterwards move all the
#  remaining *.xlsx files to the destination path (Parameter
#  $DestinationPath).

## Parameters
#
$VerbosePreference = "Continue" # Ensures verbose messages are displayed and logged

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$SetupFilePath = $GlobalScriptParameters.SharePointSetupFilePath
$ReportYear = $GlobalScriptParameters.Year
# Destination path of created files (may be a relative path)
$TargetPath = $GlobalScriptParameters.RelativeRFCFilesPath
# Filename of RFC file (should be in script folder)
$RFCfilename = $GlobalScriptParameters.RFCfilename
# Filename of Master file (should be in script folder)
$MasterFilename = $GlobalScriptParameters.MasterFilename
$MasterSharepointFolder = $GlobalScriptParameters.SharePointMasterFolder
$MasterSharepointPath = $MasterSharepointFolder + $MasterFilename

# The work sheet in the RFC which is the active sheet by default
$DefaultSheet = "Workload Plan"


function Update-Queries
{
	param($WorkbookPrm, $UpdateModePrm)

	Update-AllQueries -WorkbookPrm $WorkbookPrm

    Wait-OnAllQueries -WorkbookPrm $WorkbookPrm
}

function Create-RFC_Files
{
    param($TargetPathPrm)

	# Compute full file paths
	$CurrentPath = Get-Location
	$RFCfilepath = Join-Path $PSScriptRoot $RFCfilename
	$MasterFilepath = Join-Path $CurrentPath $MasterFilename

	# Start Excel
	$ExcelHndl = Start-Excel
	$Excel = $ExcelHndl["excel"]

	try{
		# Query the names of all project leads and rfc managers from master file
		$ProjectLeads = Get-LeadNames -ExcelHndl $ExcelHndl -MasterFilepath $MasterFilepath

		# Open RFC workbook
		Write-Verbose "Reading file $RFCfilepath"
		$RFCWorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $RFCfilepath
		$RFCWorkbook = $RFCWorkbookHndl["workbook"]
		$PropertySheet = $RFCWorkbook.Sheets.item("Properties")
		
		# Select the files to update
        $Labels = $ProjectLeads | % {"$($_)"}
		$SelectedKeys = Select-ByGUIWithLabels -CandidateList $ProjectLeads -CandidateLabels $Labels
		$AllLeadsSelected = ($ProjectLeads.Count -eq $SelectedKeys.Count)
		Write-Verbose "Selected $($SelectedKeys.Count) files"
		
		# Loop over lead names, update queries and write RFC file
		$RFCFiles = @()
        $LoopStart = Get-Date
        $i = 0
		foreach ($CurrProjLead in $SelectedKeys)
		{
            $ProjectLeadFilename = Get-ProjectLeadFilename -Name $CurrProjLead -Year $ReportYear
            $NewFilepath = Join-Path $CurrentPath "$($ProjectLeadFilename).xlsx"
			$RFCFiles += $NewFilepath

			Write-Verbose "Switch to file $NewFilepath"
			$RFCWorkbook.SaveAs($NewFilepath)

			# Set the local remote paths and the project name
			$PropSplt = @{ Sheet= $PropertySheet; KeyCol= "Property";  ValueCol= "Value" }
			$PropMasterSplt = @{Tab = "MasterProps";} + $PropSplt
			$PropFileSplt = @{Tab = "FileProps";} + $PropSplt
			Write-TableEntry @PropFileSplt -Key "Setup File Path" -Value $SetupFilePath
			Write-TableEntry @PropMasterSplt -Key "MasterPath" -Value $MasterFilepath
			Write-TableEntry @PropFileSplt -Key "Owner" -Value $CurrProjLead
			Write-TableEntry @PropMasterSplt -Key "Year" -Value $ReportYear
			
			Update-Queries -WorkbookPrm ($RFCWorkbook) -UpdateMode $CurrProjLead.UpdateMode
			
			Write-Verbose "Writing file $NewFilepath"
            $i++
            Write-Verbose "Created $i / $($SelectedKeys.Count) RFC files"
            $Remaining = ( (Get-Date) - $LoopStart).TotalSeconds / $i * ($SelectedKeys.Count - $i)
            Write-Verbose ("{0:f0} seconds remaining  `r`n" -f $Remaining)
			$RFCWorkbook.Save()
			$RFCWorkbook.Worksheets($DefaultSheet).Activate()
		}

		Close-Workbook -workbookHndl $RFCWorkbookHndl

		# Only if all rfc files succeeded do we copy them to the target folder
        foreach($filepath in $RFCFiles){
            Write-Verbose "Copy file $filepath to $TargetPathPrm"            
            Copy-Item -Path $filepath -Destination $TargetPathPrm -Force
			$targetFilePath = Join-Path -Path $TargetPathPrm -ChildPath (Split-Path -Path $filepath -Leaf)

			# Open, save and close the file to ensure proper synchronization
			$wbHndl = Open-Workbook -excelHnd $ExcelHndl -workbookFilePath $targetFilePath -isSharepoint $true
			Close-Workbook -workbookHndl $wbHndl
        }

		# Afterwards we explicitly remove such files in the target
		# folder which do not match any of the new file names. This
		# may seem more awkward than just deleting all files in that
		# folder prior to copying but workd better with one-drive
		# synchronization
		if ($AllLeadsSelected)
		{
			$allowedFileNames = $RFCFiles | ForEach-Object { [System.IO.Path]::GetFileName($_) }
			$filesInFolder = Get-ChildItem -Path $TargetPathPrm -File
			foreach ($file in $filesInFolder) {
				if ($allowedFileNames -notcontains $file.Name) {
					Write-Warning "Removing unknown file from RFC folder: $($file.FullName)"
					Remove-Item -Path $file.FullName -Force
				}
			}
		}
	}
	catch{
		Write-Error "An error occurred: $_"
		Write-Error "File: $($_.InvocationInfo.ScriptName)"
		Write-Error "Line: $($_.InvocationInfo.ScriptLineNumber)"

		# Capture the script location and line number from the call stack
		$callStack = Get-PSCallStack

		if ($callStack.Count -gt 1) {
			Write-Error "Call Stack:"
			foreach ($frame in $callStack) {
				Write-Error "  Function: $($frame.Command) - File: $($frame.ScriptName) - Line: $($frame.ScriptLineNumber)"
			}
		}
		else {
			# Fallback if no call stack is available
			Write-Error "File: $($_.InvocationInfo.ScriptName)"
			Write-Error "Line: $($_.InvocationInfo.ScriptLineNumber)"
		}

		# Capture .NET stack trace if available
		if ($_.Exception.StackTrace) {
			Write-Error "Exception Stack Trace: $($_.Exception.StackTrace)"
		}
	}
	finally	{
		Stop-Excel -excelHndl $ExcelHndl
	}
}

# Create temporary path
$TempPath = (New-TemporaryDirectory).FullName

New-FolderIfMissing -FolderPathPrm $TargetPath

# Make the target path absolute
$TargetPath = Resolve-Path -Path $TargetPath
Write-Host "Target folder: $TargetPath"

# Path of this script in file system
$ScriptPath = split-path -parent $MyInvocation.MyCommand.Definition

# Determine file to copy to destination
$TempFiles = Get-ChildItem -Path ".\*" -Include @("*.xlsx",$ScriptName)
$TempFiles | ForEach-Object { Copy-Item -Path ".\$($_.Name)"  -Destination $TempPath -Force }

# Switch to destination and create data files
cd $TempPath
Create-RFC_Files -TargetPathPrm $TargetPath

# Switch back to original destination
cd $ScriptPath

# Remove temp folder
Write-Host "Remove folder $TempPath"
Remove-Item -Recurse -Force $TempPath


Pause
