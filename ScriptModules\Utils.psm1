# -*- coding: utf-8-with-signature -*-
$VerbosePreference = "Continue" # Ensures verbose messages are displayed and logged

# Create system compliant temp directory and return path
function New-TemporaryDirectory 
{
    $parent = [System.IO.Path]::GetTempPath()
    [string] $name = [System.Guid]::NewGuid()
    New-Item -ItemType Directory -Path (Join-Path $parent $name)
}


# Substitutes common special characters in a simple fashion
function Get-ValidFilename
{
    param($name)
    $name = $name -replace "ä", "ae"
    $name = $name -replace "ö", "oe"
    $name = $name -replace "ü", "ue"
    $name = $name -replace "é", "e"
	$name = $name -replace "è", "e"
    $name = $name -replace "ß", "ss"
    $name = $name -replace "[^A-Za-z0-9-_]", "_"
    return $name
}

# Return the standardized RFC filename for the given lead name and year
function Get-ProjectLeadFilename
{
    param(
		[Parameter(Mandatory)] $Name,
		[Parameter(Mandatory)] $Year
	)

    $projectFilename = $Name + "__$($Year)"
    $projectFilename = Get-ValidFilename -name $projectFilename
    return $projectFilename
}


# Interrupt flow and prompt for key. Abort on Escape.
function Invoke-Continue {
    Write-Verbose "Press any key to continue, or press Escape to abort..."
    $key = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    if ($key.VirtualKeyCode -eq 27) { # Check if the pressed key is Escape
        Write-Verbose "Aborted."
        exit
    }
}

function Get-LeadNames
{
	param(
		[Parameter(Mandatory)] $ExcelHndl,
		[Parameter(Mandatory)] $MasterFilepath
	)

	Write-Verbose "Read leads from master file: $MasterFilepath"
	$MasterWorkbookHndl = Open-Workbook -excelHnd $ExcelHndl -workbookFilePath $MasterFilepath
	$MasterWorkbook = $MasterWorkbookHndl["workbook"]
	$LeadSheet = $MasterWorkbook.Sheets.item("All Owners")
	$ProjectLeads = Read-TableColumn -Sheet $LeadSheet -Tab "GetAllOwners" -Col "RFC Manager"
	Close-Workbook -workbookHndl $MasterWorkbookHndl
    Write-Verbose "Found $($ProjectLeads.Count) RFC Managers in master"

	return $ProjectLeads
}


# Read value from a named Excel table via a key, a key column and a value column
function Read-TabEntry
{
	param(
		[Parameter(Mandatory)] $Sheet, # Sheet containing table
		[Parameter(Mandatory)] $Tab, # Table name
		[Parameter(Mandatory)] $KeyCol, # Name of column which holds the key
		[Parameter(Mandatory)] $ValueCol, # Name of column which holds the value
		[Parameter(Mandatory)] $Key, # The key
		$Default # The default value in case the key cannot be
				 # found. If not given, an error is thrown if the key
				 # cannot be found
	)

	$KeyRange = $Sheet.Range("$($Tab)[$($KeyCol)]")
	$KeyCell = $KeyRange.Find($Key)
	if ($null -eq $KeyCell){
		if ($null -eq $Default){
			throw "Could not find tab entry $Key in column $KeyCol !"
		}
		else{
			return $Default
		}
	}
	$ValueRange = $Sheet.Range("$($Tab)[$($ValueCol)]")
	$ValueCell = $Sheet.Cells($KeyCell.Row, $ValueRange.Column)
	$Value = $ValueCell.Value2
	return $Value
}

# Read some properties from the master file
# Currently this includes
# .Leads: the superset of project leads and RFC managers
# .Year: the reporting year
function Read-MasterFileProperties
{
	param([Parameter(Mandatory)]$MasterWorkbookPrm, [Parameter(Mandatory)]$UpdateModePrm)

	$ProjectSheet = $MasterWorkbookPrm.Sheets.item("Projects")
    $NumProjects = $ProjectSheet.Range("Proj_Master").Rows.Count
    $GetProjectLeadFn = { param ($row)
        				  $ProjectSheet.Range("Proj_Master[Project Lead]").Cells($row,1).Value2
						}

    $EmployeeSheet = $MasterWorkbookPrm.Sheets.item("All Employees")
    $NumEmployees = $EmployeeSheet.Range("AllEmployees").Rows.Count
    $GetEmployeeMgrFn = { param ($row)
        				  $EmployeeSheet.Range("AllEmployees[RFC Manager]").Cells($row,1).Value2
						}

	$Year = Read-TabEntry -Sheet $MasterWorkbookPrm.Sheets.item("Parameters") -Tab "FilePaths" `
	  -KeyCol "Property" -ValueCol "Value" -Key "Year"
	
	# Look up the update mode for a given RFC manager
	function Get-UpdateMode {
		param($LeadName)
		
		# Determine update mode for this lead
		$UpdateMode = Read-TabEntry -Sheet $MasterWorkbookPrm.Sheets.item("Parameters") `
		  -Tab "UpdateModeTab" -KeyCol "RFC Manager" -ValueCol "Mode" -Key $Lead -Default $UpdateModePrm
		return $UpdateMode
	}

    $ProjectLeads = [ordered]@{}

    # For a given table column with manager names (with duplicates), store each name into the hash table
    # $ProjectLeads and determine the update mode
    function Add-GeneratedLeads{
		param($LeadGenerator, $LeadCount)

		for ($i=1; $i -le $LeadCount; $i++)
		{
			# Determine project lead
			$Lead = (&$LeadGenerator($i))

			# Add entry if it does not exist yet
			$HasKey = $ProjectLeads.Keys -contains $Lead
			if ( $Lead -and ($Lead.Trim() -ne "NA") -and (-not $HasKey) )
			{
				$UpdateMode = Get-UpdateMode -LeadName $Lead
				Write-Verbose "Found RFC manager $Lead with update mode $UpdateMode in master"
				$ProjectLeads.add($Lead, @{Lead = $Lead; Year = $Year; UpdateMode = $UpdateMode})
			}
		}
	}

	Add-GeneratedLeads -LeadGenerator $GetProjectLeadFn -LeadCount $NumProjects
	Add-GeneratedLeads -LeadGenerator $GetEmployeeMgrFn -LeadCount $NumEmployees
	
	$MasterProps = @{}
	$MasterProps.add("Leads", $ProjectLeads)
	$MasterProps.add("Year",$Year)

	return $MasterProps	
}


# Refresh all query tables within this workbook
# WorkbookPrm : The excel workbook to be refreshed
# ExceptionListPrm : A list of names which should be exempt from the update
# WhiteListPrm : A white list of names which should be subject to the update
function Update-AllQueries
{
    param($WorkbookPrm, $ExceptionListPrm = @(), $WhiteListPrm = @() )
    
    foreach ($sheet in ($WorkbookPrm.Sheets)) {
        $sheet.ListObjects | ForEach-Object{
            $Table = $_
            if ($null -eq $Table){ throw "Table is not valid!" }

            if ( ($WhiteListPrm.count -eq 0) -or ($Table.DisplayName -in  $WhiteListPrm) )
            {
                if ($Table.QueryTable -and ($Table.DisplayName -notin $ExceptionListPrm) ) {
					if ($Table.QueryTable.BackgroundQuery){
						Write-Verbose "Found a background query! Changing it to synched query."
						$Table.QueryTable.BackgroundQuery = $false
					}
                    Write-Verbose "Refresh $($Table.DisplayName)"
                    $Table.QueryTable.Refresh() | out-null
                }
            }
        }
    }
}

# Refresh all query tables within this workbook
# WorkbookPrm : The excel workbook to be refreshed
# MatchStrPrm : The string which should be in the display name of the table
# ShouldMatchPrm : Bool value indicating whether it must match (TRUE) or
# it must not match (FALSE)
# AllTablesPrm : A list of names which should hold all possible query table
# display names. If a query table's display name is not whithin that list,
# this function throws and error
function Update-MatchingQueries
{
	param($WorkbookPrm, $MatchStrPrm, $ShouldMatchPrm, $AllTablesPrm)
	
	foreach ($sheet in ($WorkbookPrm.Sheets)) {
		$sheet.ListObjects | ForEach-Object{
			$Table = $_
			if ($null -eq $Table){ throw "Table is not valid!" }
			
			if ( ($Table.DisplayName -notin $AllTablesPrm) -and $AllTablesPrm){
				throw "Table $($Table.DisplayName) is not a valid name!"
			}

			$DoesMatch = ($Table.DisplayName -match $MatchStrPrm) -eq $ShouldMatchPrm
			if ($Table.QueryTable -and $DoesMatch) {
				if ($Table.QueryTable.BackgroundQuery){
					Write-Verbose "Found a background query! Changing it to synched query."
					$Table.QueryTable.BackgroundQuery = $false
				}

				Write-Verbose "Refresh $($Table.DisplayName)"
				$Table.QueryTable.Refresh() | out-null
			}
		}
	}
}

# Wait for all query updates to finish
function Wait-OnAllQueries
{
    param($WorkbookPrm)

    foreach ($sheet in ($WorkbookPrm.Sheets)) {
        $sheet.ListObjects | ForEach-Object{
            $Table = $_
            while ($Table.QueryTable.Refreshing) {
                Write-Verbose "Sleep while waiting for query update of table $($Table.DisplayName)"
                Start-Sleep -Seconds 1
            }
        }
    }
}

function New-FolderIfMissing
{
    param ($FolderPathPrm)

    if (-not (Test-Path -Path $FolderPathPrm -PathType Container) ){
        $ThisNewItem = New-Item -ItemType Directory -Force -Path $FolderPathPrm
        Write-Verbose "Created target folder $($ThisNewItem.Fullname)"
    }
}

# Starts a timer and returns the start time
function Start-Timer
{
    return Get-Date
}

# Stops a timer, calculates the duration, and outputs the result
function Stop-Timer
{
    param (
        [Parameter(Mandatory=$true)]
        [DateTime] $StartTime,
        
        [Parameter(Mandatory=$true)]
        [string] $OperationName
    )
    
    $endTime = Get-Date
    $duration = $endTime - $StartTime
    
    $formattedTime = "{0:hh\:mm\:ss\.fff}" -f $duration
    $totalSeconds = $duration.TotalSeconds
    
    Write-Verbose "Operation '$OperationName' completed in $formattedTime ($($totalSeconds.ToString('0.00')) seconds)"
}

# Checks progress of an ongoing operation and estimates remaining time
function Check-Timer
{
    param (
        [Parameter(Mandatory=$true)]
        [DateTime] $StartTime,
        
        [Parameter(Mandatory=$true)]
        [string] $OperationName,
        
        [Parameter(Mandatory=$true)]
        [float] $CompletionFactor
    )
    
    # Validate completion factor is between 0 and 1
    if ($CompletionFactor -lt 0 -or $CompletionFactor -gt 1) {
        throw "CompletionFactor must be between 0 and 1"
    }
    
    # If completion factor is 0, we can't estimate remaining time
    if ($CompletionFactor -eq 0) {
        Write-Verbose "Operation '$OperationName' - 0% complete, elapsed time: 00:00:00"
        return
    }
    
    $currentTime = Get-Date
    $elapsedTime = $currentTime - $StartTime
    $elapsedSeconds = $elapsedTime.TotalSeconds
    
    # Calculate estimated total time and remaining time
    $estimatedTotalSeconds = $elapsedSeconds / $CompletionFactor
    $remainingSeconds = $estimatedTotalSeconds - $elapsedSeconds
    
    # Format times for display
    $elapsedFormatted = "{0:hh\:mm\:ss\.fff}" -f $elapsedTime
    $remainingFormatted = "{0:hh\:mm\:ss\.fff}" -f ([timespan]::FromSeconds($remainingSeconds))
    
    # Convert completion factor to percentage for display
    $percentComplete = $CompletionFactor * 100
    
    Write-Verbose ("Operation '$OperationName' - {0:F1}% complete, elapsed: $elapsedFormatted, estimated remaining: $remainingFormatted" -f $percentComplete)
}
